2025-07-30 16:12:52 | ERROR | core.fault_analyzer:__init__:39 | LLM未传入或不可用，故障分析功能将受限！
2025-07-30 16:12:52 | ERROR | core.fault_analyzer:__init__:66 | 知识库初始化失败: 请使用 get_unified_retriever_compat 以确保兼容性
2025-07-30 16:12:54 | ERROR | core.fault_analyzer:_initialize_chains:105 | 故障分析链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-30 16:12:54 | ERROR | core.unified_startup_manager:initialize:36 | 初始化失败: 'EnvironmentManager' object has no attribute 'check_environment'
2025-07-30 16:12:54 | ERROR | __main__:async_main:27 | 系统初始化失败
2025-07-30 16:13:35 | ERROR | core.fault_analyzer:__init__:39 | LLM未传入或不可用，故障分析功能将受限！
2025-07-30 16:13:35 | ERROR | core.fault_analyzer:__init__:66 | 知识库初始化失败: 请使用 get_unified_retriever_compat 以确保兼容性
2025-07-30 16:13:37 | ERROR | core.fault_analyzer:_initialize_chains:105 | 故障分析链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-30 16:13:37 | ERROR | core.unified_startup_manager:initialize:36 | 初始化失败: 'EnvironmentManager' object has no attribute 'check_environment'
2025-07-30 16:13:37 | ERROR | __main__:async_main:27 | 系统初始化失败
2025-07-30 16:48:56.610 | ERROR    | core.system_initializer:full_initialization:256 - ❌ 系统初始化失败: 'DependencyManager' object has no attribute 'check_all_dependencies'
2025-07-30 16:48:56 | ERROR | core.system_initializer:full_initialization:256 | ❌ 系统初始化失败: 'DependencyManager' object has no attribute 'check_all_dependencies'
2025-07-30 16:48:56.611 | ERROR    | __main__:main:352 - ❌ 系统初始化失败
2025-07-30 16:48:56 | ERROR | __main__:main:352 | ❌ 系统初始化失败
2025-07-30 16:50:23.924 | ERROR    | core.fault_analyzer:__init__:39 - LLM未传入或不可用，故障分析功能将受限！
2025-07-30 16:50:23 | ERROR | core.fault_analyzer:__init__:39 | LLM未传入或不可用，故障分析功能将受限！
2025-07-30 16:50:23.925 | ERROR    | core.fault_analyzer:__init__:66 - 知识库初始化失败: 请使用 get_unified_retriever_compat 以确保兼容性
2025-07-30 16:50:23 | ERROR | core.fault_analyzer:__init__:66 | 知识库初始化失败: 请使用 get_unified_retriever_compat 以确保兼容性
2025-07-30 16:50:25.718 | ERROR    | core.fault_analyzer:_initialize_chains:105 - 故障分析链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-30 16:50:25 | ERROR | core.fault_analyzer:_initialize_chains:105 | 故障分析链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-30 16:50:28.235 | ERROR    | data_processing.modern_chroma_manager:_init_collection:102 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 16:50:28 | ERROR | data_processing.modern_chroma_manager:_init_collection:102 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 16:50:59.779 | ERROR    | core.fault_analyzer:__init__:39 - LLM未传入或不可用，故障分析功能将受限！
2025-07-30 16:50:59 | ERROR | core.fault_analyzer:__init__:39 | LLM未传入或不可用，故障分析功能将受限！
2025-07-30 16:50:59.779 | ERROR    | core.fault_analyzer:__init__:66 - 知识库初始化失败: 请使用 get_unified_retriever_compat 以确保兼容性
2025-07-30 16:50:59 | ERROR | core.fault_analyzer:__init__:66 | 知识库初始化失败: 请使用 get_unified_retriever_compat 以确保兼容性
2025-07-30 16:51:01.658 | ERROR    | core.fault_analyzer:_initialize_chains:105 - 故障分析链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-30 16:51:01 | ERROR | core.fault_analyzer:_initialize_chains:105 | 故障分析链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-30 16:51:03.978 | ERROR    | data_processing.modern_chroma_manager:_init_collection:102 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 16:51:03 | ERROR | data_processing.modern_chroma_manager:_init_collection:102 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 16:51:58.053 | ERROR    | core.fault_analyzer:__init__:39 - LLM未传入或不可用，故障分析功能将受限！
2025-07-30 16:51:58 | ERROR | core.fault_analyzer:__init__:39 | LLM未传入或不可用，故障分析功能将受限！
2025-07-30 16:51:58.054 | ERROR    | core.fault_analyzer:__init__:66 - 知识库初始化失败: 请使用 get_unified_retriever_compat 以确保兼容性
2025-07-30 16:51:58 | ERROR | core.fault_analyzer:__init__:66 | 知识库初始化失败: 请使用 get_unified_retriever_compat 以确保兼容性
2025-07-30 16:51:59.816 | ERROR    | core.fault_analyzer:_initialize_chains:105 - 故障分析链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-30 16:51:59 | ERROR | core.fault_analyzer:_initialize_chains:105 | 故障分析链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-30 16:52:01.806 | ERROR    | data_processing.modern_chroma_manager:_init_collection:102 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 16:52:01 | ERROR | data_processing.modern_chroma_manager:_init_collection:102 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 16:52:39.008 | ERROR    | langchain_modules.llms.llm_factory:create_llm:36 - LLM初始化失败 (mock): cannot import name 'get_mock_llm' from 'core.mock_llm' (G:\my-dl-dmx\core\mock_llm.py)
2025-07-30 16:52:39 | ERROR | langchain_modules.llms.llm_factory:create_llm:36 | LLM初始化失败 (mock): cannot import name 'get_mock_llm' from 'core.mock_llm' (G:\my-dl-dmx\core\mock_llm.py)
2025-07-30 16:52:39.008 | ERROR    | core.fault_analyzer:__init__:49 - LLM未传入或不可用，故障分析功能将受限！
2025-07-30 16:52:39 | ERROR | core.fault_analyzer:__init__:49 | LLM未传入或不可用，故障分析功能将受限！
2025-07-30 16:52:39.012 | ERROR    | core.fault_analyzer:__init__:86 - 知识库初始化失败: 请使用 get_unified_retriever_compat 以确保兼容性
2025-07-30 16:52:39 | ERROR | core.fault_analyzer:__init__:86 | 知识库初始化失败: 请使用 get_unified_retriever_compat 以确保兼容性
2025-07-30 16:52:40.921 | ERROR    | core.fault_analyzer:_initialize_chains:125 - 故障分析链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-30 16:52:40 | ERROR | core.fault_analyzer:_initialize_chains:125 | 故障分析链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-30 16:52:43.374 | ERROR    | data_processing.modern_chroma_manager:_init_collection:102 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 16:52:43 | ERROR | data_processing.modern_chroma_manager:_init_collection:102 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 16:53:29.038 | ERROR    | langchain_modules.llms.llm_factory:create_llm:36 - LLM初始化失败 (mock): cannot import name 'get_mock_llm' from 'core.mock_llm' (G:\my-dl-dmx\core\mock_llm.py)
2025-07-30 16:53:29 | ERROR | langchain_modules.llms.llm_factory:create_llm:36 | LLM初始化失败 (mock): cannot import name 'get_mock_llm' from 'core.mock_llm' (G:\my-dl-dmx\core\mock_llm.py)
2025-07-30 16:53:29.039 | ERROR    | core.fault_analyzer:__init__:49 - LLM未传入或不可用，故障分析功能将受限！
2025-07-30 16:53:29 | ERROR | core.fault_analyzer:__init__:49 | LLM未传入或不可用，故障分析功能将受限！
2025-07-30 16:53:29.040 | ERROR    | core.fault_analyzer:__init__:86 - 知识库初始化失败: 请使用 get_unified_retriever_compat 以确保兼容性
2025-07-30 16:53:29 | ERROR | core.fault_analyzer:__init__:86 | 知识库初始化失败: 请使用 get_unified_retriever_compat 以确保兼容性
2025-07-30 16:53:30.763 | ERROR    | core.fault_analyzer:_initialize_chains:125 - 故障分析链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-30 16:53:30 | ERROR | core.fault_analyzer:_initialize_chains:125 | 故障分析链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-30 16:53:32.734 | ERROR    | data_processing.modern_chroma_manager:_init_collection:102 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 16:53:32 | ERROR | data_processing.modern_chroma_manager:_init_collection:102 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 16:54:04.657 | ERROR    | langchain_modules.llms.llm_factory:create_llm:36 - LLM初始化失败 (mock): cannot import name 'get_mock_llm' from 'core.mock_llm' (G:\my-dl-dmx\core\mock_llm.py)
2025-07-30 16:54:04 | ERROR | langchain_modules.llms.llm_factory:create_llm:36 | LLM初始化失败 (mock): cannot import name 'get_mock_llm' from 'core.mock_llm' (G:\my-dl-dmx\core\mock_llm.py)
2025-07-30 16:54:04.658 | ERROR    | core.fault_analyzer:__init__:49 - LLM未传入或不可用，故障分析功能将受限！
2025-07-30 16:54:04 | ERROR | core.fault_analyzer:__init__:49 | LLM未传入或不可用，故障分析功能将受限！
2025-07-30 16:54:04.658 | ERROR    | core.fault_analyzer:__init__:86 - 知识库初始化失败: 请使用 get_unified_retriever_compat 以确保兼容性
2025-07-30 16:54:04 | ERROR | core.fault_analyzer:__init__:86 | 知识库初始化失败: 请使用 get_unified_retriever_compat 以确保兼容性
2025-07-30 16:54:06.397 | ERROR    | core.fault_analyzer:_initialize_chains:125 - 故障分析链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-30 16:54:06 | ERROR | core.fault_analyzer:_initialize_chains:125 | 故障分析链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-30 16:54:08.446 | ERROR    | data_processing.modern_chroma_manager:_init_collection:102 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 16:54:08 | ERROR | data_processing.modern_chroma_manager:_init_collection:102 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 16:55:50.901 | ERROR    | langchain_modules.llms.llm_factory:create_llm:36 - LLM初始化失败 (mock): cannot import name 'get_mock_llm' from 'core.mock_llm' (G:\my-dl-dmx\core\mock_llm.py)
2025-07-30 16:55:50 | ERROR | langchain_modules.llms.llm_factory:create_llm:36 | LLM初始化失败 (mock): cannot import name 'get_mock_llm' from 'core.mock_llm' (G:\my-dl-dmx\core\mock_llm.py)
2025-07-30 16:55:50.902 | ERROR    | core.fault_analyzer:__init__:49 - LLM未传入或不可用，故障分析功能将受限！
2025-07-30 16:55:50 | ERROR | core.fault_analyzer:__init__:49 | LLM未传入或不可用，故障分析功能将受限！
2025-07-30 16:55:50.902 | ERROR    | core.fault_analyzer:__init__:87 - 知识库初始化失败: 请使用 get_unified_retriever_compat 以确保兼容性
2025-07-30 16:55:50 | ERROR | core.fault_analyzer:__init__:87 | 知识库初始化失败: 请使用 get_unified_retriever_compat 以确保兼容性
2025-07-30 16:55:52.584 | ERROR    | core.fault_analyzer:_initialize_chains:126 - 故障分析链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-30 16:55:52 | ERROR | core.fault_analyzer:_initialize_chains:126 | 故障分析链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-30 16:55:54.473 | ERROR    | data_processing.modern_chroma_manager:_init_collection:102 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 16:55:54 | ERROR | data_processing.modern_chroma_manager:_init_collection:102 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 16:56:35.662 | ERROR    | core.service_manager:get_service:69 - 创建服务失败 fault_analyzer: 'EnhancedDeepSeekIntegration' object has no attribute 'get_llm'
2025-07-30 16:56:35 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: 'EnhancedDeepSeekIntegration' object has no attribute 'get_llm'
2025-07-30 16:56:37.749 | ERROR    | data_processing.modern_chroma_manager:_init_collection:102 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 16:56:37 | ERROR | data_processing.modern_chroma_manager:_init_collection:102 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 16:57:16.015 | ERROR    | core.service_manager:get_service:69 - 创建服务失败 fault_analyzer: 'EnhancedDeepSeekIntegration' object has no attribute 'get_llm'
2025-07-30 16:57:16 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: 'EnhancedDeepSeekIntegration' object has no attribute 'get_llm'
2025-07-30 16:57:17.874 | ERROR    | data_processing.modern_chroma_manager:_init_collection:102 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 16:57:17 | ERROR | data_processing.modern_chroma_manager:_init_collection:102 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 16:57:56.769 | ERROR    | core.service_manager:get_service:69 - 创建服务失败 fault_analyzer: 'EnhancedDeepSeekIntegration' object has no attribute 'get_llm'
2025-07-30 16:57:56 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: 'EnhancedDeepSeekIntegration' object has no attribute 'get_llm'
2025-07-30 16:59:04.866 | ERROR    | core.service_manager:get_service:69 - 创建服务失败 fault_analyzer: 
2025-07-30 16:59:04 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: 
2025-07-30 16:59:04.868 | ERROR    | core.service_manager:get_service:69 - 创建服务失败 equipment_manager: cannot import name 'EquipmentManager' from '<unknown module name>' (unknown location)
2025-07-30 16:59:04 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: cannot import name 'EquipmentManager' from '<unknown module name>' (unknown location)
2025-07-30 16:59:04.877 | ERROR    | __main__:start_flask_server:122 - Flask服务器启动失败: cannot import name 'app' from '<unknown module name>' (unknown location)
2025-07-30 16:59:04 | ERROR | __main__:start_flask_server:122 | Flask服务器启动失败: cannot import name 'app' from '<unknown module name>' (unknown location)
2025-07-30 16:59:04.877 | ERROR    | __main__:start_optimized_server:297 - ❌ 主服务器启动失败: cannot import name 'app' from '<unknown module name>' (unknown location)
2025-07-30 16:59:04 | ERROR | __main__:start_optimized_server:297 | ❌ 主服务器启动失败: cannot import name 'app' from '<unknown module name>' (unknown location)
2025-07-30 17:21:10.124 | ERROR    | core.service_manager:get_service:69 - 创建服务失败 fault_analyzer: 'EnhancedDeepSeekIntegration' object has no attribute 'get_llm'
2025-07-30 17:21:10 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: 'EnhancedDeepSeekIntegration' object has no attribute 'get_llm'
2025-07-30 17:21:12.036 | ERROR    | data_processing.modern_chroma_manager:_init_collection:102 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 17:21:12 | ERROR | data_processing.modern_chroma_manager:_init_collection:102 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 17:27:31.126 | ERROR    | core.service_manager:get_service:69 - 创建服务失败 fault_analyzer: 'EnhancedDeepSeekIntegration' object has no attribute 'get_llm'
2025-07-30 17:27:31 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: 'EnhancedDeepSeekIntegration' object has no attribute 'get_llm'
2025-07-30 17:27:33.045 | ERROR    | data_processing.modern_chroma_manager:_init_collection:102 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 17:27:33 | ERROR | data_processing.modern_chroma_manager:_init_collection:102 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 17:29:25.415 | ERROR    | core.llm_manager:create_llm:24 - 创建LLM实例失败: 'dict' object has no attribute 'lower'
2025-07-30 17:29:25 | ERROR | core.llm_manager:create_llm:24 | 创建LLM实例失败: 'dict' object has no attribute 'lower'
2025-07-30 17:29:25.415 | ERROR    | core.fault_analyzer:__init__:50 - LLM未传入或不可用，故障分析功能将受限！
2025-07-30 17:29:25 | ERROR | core.fault_analyzer:__init__:50 | LLM未传入或不可用，故障分析功能将受限！
2025-07-30 17:29:25.416 | ERROR    | core.fault_analyzer:__init__:88 - 知识库初始化失败: 请使用 get_unified_retriever_compat 以确保兼容性
2025-07-30 17:29:25 | ERROR | core.fault_analyzer:__init__:88 | 知识库初始化失败: 请使用 get_unified_retriever_compat 以确保兼容性
2025-07-30 17:29:27.100 | ERROR    | core.fault_analyzer:_initialize_chains:127 - 故障分析链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-30 17:29:27 | ERROR | core.fault_analyzer:_initialize_chains:127 | 故障分析链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-30 17:29:29.052 | ERROR    | data_processing.modern_chroma_manager:_init_collection:119 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 17:29:29 | ERROR | data_processing.modern_chroma_manager:_init_collection:119 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 17:30:49.843 | ERROR    | core.llm_manager:create_llm:24 - 创建LLM实例失败: 'dict' object has no attribute 'lower'
2025-07-30 17:30:49 | ERROR | core.llm_manager:create_llm:24 | 创建LLM实例失败: 'dict' object has no attribute 'lower'
2025-07-30 17:30:49.843 | ERROR    | core.fault_analyzer:__init__:50 - LLM未传入或不可用，故障分析功能将受限！
2025-07-30 17:30:49 | ERROR | core.fault_analyzer:__init__:50 | LLM未传入或不可用，故障分析功能将受限！
2025-07-30 17:30:49.844 | ERROR    | core.fault_analyzer:__init__:88 - 知识库初始化失败: 请使用 get_unified_retriever_compat 以确保兼容性
2025-07-30 17:30:49 | ERROR | core.fault_analyzer:__init__:88 | 知识库初始化失败: 请使用 get_unified_retriever_compat 以确保兼容性
2025-07-30 17:30:51.566 | ERROR    | core.fault_analyzer:_initialize_chains:127 - 故障分析链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-30 17:30:51 | ERROR | core.fault_analyzer:_initialize_chains:127 | 故障分析链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-30 17:30:53.550 | ERROR    | data_processing.modern_chroma_manager:_init_collection:119 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 17:30:53 | ERROR | data_processing.modern_chroma_manager:_init_collection:119 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 17:32:00.269 | ERROR    | core.llm_manager:create_llm:24 - 创建LLM实例失败: 'dict' object has no attribute 'lower'
2025-07-30 17:32:00 | ERROR | core.llm_manager:create_llm:24 | 创建LLM实例失败: 'dict' object has no attribute 'lower'
2025-07-30 17:32:00.269 | ERROR    | core.fault_analyzer:__init__:50 - LLM未传入或不可用，故障分析功能将受限！
2025-07-30 17:32:00 | ERROR | core.fault_analyzer:__init__:50 | LLM未传入或不可用，故障分析功能将受限！
2025-07-30 17:32:00.269 | ERROR    | core.fault_analyzer:__init__:88 - 知识库初始化失败: 请使用 get_unified_retriever_compat 以确保兼容性
2025-07-30 17:32:00 | ERROR | core.fault_analyzer:__init__:88 | 知识库初始化失败: 请使用 get_unified_retriever_compat 以确保兼容性
2025-07-30 17:32:01.911 | ERROR    | core.fault_analyzer:_initialize_chains:127 - 故障分析链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-30 17:32:01 | ERROR | core.fault_analyzer:_initialize_chains:127 | 故障分析链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-30 17:32:03.866 | ERROR    | data_processing.modern_chroma_manager:_init_collection:119 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 17:32:03 | ERROR | data_processing.modern_chroma_manager:_init_collection:119 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 17:33:56.742 | ERROR    | core.llm_manager:create_llm:29 - 模型名称必须是字符串，而不是 <class 'dict'>
2025-07-30 17:33:56 | ERROR | core.llm_manager:create_llm:29 | 模型名称必须是字符串，而不是 <class 'dict'>
2025-07-30 17:33:56.742 | ERROR    | core.fault_analyzer:__init__:50 - LLM未传入或不可用，故障分析功能将受限！
2025-07-30 17:33:56 | ERROR | core.fault_analyzer:__init__:50 | LLM未传入或不可用，故障分析功能将受限！
2025-07-30 17:33:56.743 | ERROR    | core.fault_analyzer:__init__:88 - 知识库初始化失败: 请使用 get_unified_retriever_compat 以确保兼容性
2025-07-30 17:33:56 | ERROR | core.fault_analyzer:__init__:88 | 知识库初始化失败: 请使用 get_unified_retriever_compat 以确保兼容性
2025-07-30 17:33:58.561 | ERROR    | core.fault_analyzer:_initialize_chains:127 - 故障分析链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-30 17:33:58 | ERROR | core.fault_analyzer:_initialize_chains:127 | 故障分析链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-30 17:34:00.678 | ERROR    | data_processing.modern_chroma_manager:_init_collection:119 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 17:34:00 | ERROR | data_processing.modern_chroma_manager:_init_collection:119 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 17:39:28.941 | ERROR    | core.llm_manager:create_llm:47 - 创建LLM实例失败: cannot import name 'DeepSeek' from 'langchain.llms' (C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\langchain\llms\__init__.py)
2025-07-30 17:39:28 | ERROR | core.llm_manager:create_llm:47 | 创建LLM实例失败: cannot import name 'DeepSeek' from 'langchain.llms' (C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\langchain\llms\__init__.py)
2025-07-30 17:39:28.941 | ERROR    | core.fault_analyzer:__init__:51 - LLM初始化失败: LLM初始化失败
2025-07-30 17:39:28 | ERROR | core.fault_analyzer:__init__:51 | LLM初始化失败: LLM初始化失败
2025-07-30 17:39:28.942 | ERROR    | core.fault_analyzer:__init__:56 - LLM未传入或不可用，故障分析功能将受限！
2025-07-30 17:39:28 | ERROR | core.fault_analyzer:__init__:56 | LLM未传入或不可用，故障分析功能将受限！
2025-07-30 17:39:29.100 | ERROR    | core.fault_analyzer:__init__:96 - 知识库初始化失败: 'RetrievalResponse' object has no attribute 'get'
2025-07-30 17:39:29 | ERROR | core.fault_analyzer:__init__:96 | 知识库初始化失败: 'RetrievalResponse' object has no attribute 'get'
2025-07-30 17:39:32.584 | ERROR    | data_processing.modern_chroma_manager:_init_collection:119 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 17:39:32 | ERROR | data_processing.modern_chroma_manager:_init_collection:119 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 17:42:36.979 | ERROR    | core.llm_manager:create_llm:52 - 创建LLM实例失败: cannot import name 'DeepSeek' from 'langchain.llms' (C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\langchain\llms\__init__.py)
2025-07-30 17:42:36 | ERROR | core.llm_manager:create_llm:52 | 创建LLM实例失败: cannot import name 'DeepSeek' from 'langchain.llms' (C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\langchain\llms\__init__.py)
2025-07-30 17:42:36.980 | ERROR    | core.fault_analyzer:__init__:51 - LLM初始化失败: LLM初始化失败
2025-07-30 17:42:36 | ERROR | core.fault_analyzer:__init__:51 | LLM初始化失败: LLM初始化失败
2025-07-30 17:42:36.980 | ERROR    | core.fault_analyzer:__init__:56 - LLM未传入或不可用，故障分析功能将受限！
2025-07-30 17:42:36 | ERROR | core.fault_analyzer:__init__:56 | LLM未传入或不可用，故障分析功能将受限！
2025-07-30 17:42:37.134 | ERROR    | core.fault_analyzer:__init__:94 - 知识库检索异常，建议检查embeddings和检索服务！
2025-07-30 17:42:37 | ERROR | core.fault_analyzer:__init__:94 | 知识库检索异常，建议检查embeddings和检索服务！
2025-07-30 17:42:40.736 | ERROR    | data_processing.modern_chroma_manager:_init_collection:126 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 17:42:40 | ERROR | data_processing.modern_chroma_manager:_init_collection:126 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 17:46:12.453 | ERROR    | core.llm_manager:create_llm:52 - 创建LLM实例失败: cannot import name 'DeepSeek' from 'langchain.llms' (C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\langchain\llms\__init__.py)
2025-07-30 17:46:12 | ERROR | core.llm_manager:create_llm:52 | 创建LLM实例失败: cannot import name 'DeepSeek' from 'langchain.llms' (C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\langchain\llms\__init__.py)
2025-07-30 17:46:12.454 | ERROR    | core.fault_analyzer:__init__:51 - LLM初始化失败: LLM初始化失败
2025-07-30 17:46:12 | ERROR | core.fault_analyzer:__init__:51 | LLM初始化失败: LLM初始化失败
2025-07-30 17:46:12.454 | ERROR    | core.fault_analyzer:__init__:56 - LLM未传入或不可用，故障分析功能将受限！
2025-07-30 17:46:12 | ERROR | core.fault_analyzer:__init__:56 | LLM未传入或不可用，故障分析功能将受限！
2025-07-30 17:46:12.454 | ERROR    | core.fault_analyzer:__init__:103 - 知识库初始化失败: 请使用 get_unified_retriever_compat 以确保兼容性
2025-07-30 17:46:12 | ERROR | core.fault_analyzer:__init__:103 | 知识库初始化失败: 请使用 get_unified_retriever_compat 以确保兼容性
2025-07-30 17:46:16.120 | ERROR    | data_processing.modern_chroma_manager:_init_collection:126 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 17:46:16 | ERROR | data_processing.modern_chroma_manager:_init_collection:126 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 17:51:32.793 | ERROR    | core.llm_manager:create_llm:52 - 请安装langchain-community: pip install -U langchain-community
2025-07-30 17:51:32 | ERROR | core.llm_manager:create_llm:52 | 请安装langchain-community: pip install -U langchain-community
2025-07-30 17:51:32.794 | ERROR    | core.fault_analyzer:__init__:51 - LLM初始化失败: LLM初始化失败
2025-07-30 17:51:32 | ERROR | core.fault_analyzer:__init__:51 | LLM初始化失败: LLM初始化失败
2025-07-30 17:51:32.794 | ERROR    | core.fault_analyzer:__init__:56 - LLM未传入或不可用，故障分析功能将受限！
2025-07-30 17:51:32 | ERROR | core.fault_analyzer:__init__:56 | LLM未传入或不可用，故障分析功能将受限！
2025-07-30 17:51:35.000 | ERROR    | core.fault_analyzer:_initialize_chains:154 - ❌ 推理链初始化失败：LLM不可用
2025-07-30 17:51:35 | ERROR | core.fault_analyzer:_initialize_chains:154 | ❌ 推理链初始化失败：LLM不可用
2025-07-30 17:51:37.847 | ERROR    | data_processing.modern_chroma_manager:_init_collection:126 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 17:51:37 | ERROR | data_processing.modern_chroma_manager:_init_collection:126 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 17:53:32.769 | ERROR    | core.llm_manager:create_llm:53 - 无法导入DeepSeek，请确保安装了正确的依赖
2025-07-30 17:53:32 | ERROR | core.llm_manager:create_llm:53 | 无法导入DeepSeek，请确保安装了正确的依赖
2025-07-30 17:53:32.770 | ERROR    | core.fault_analyzer:__init__:51 - LLM初始化失败: LLM初始化失败
2025-07-30 17:53:32 | ERROR | core.fault_analyzer:__init__:51 | LLM初始化失败: LLM初始化失败
2025-07-30 17:53:32.770 | ERROR    | core.fault_analyzer:__init__:56 - LLM未传入或不可用，故障分析功能将受限！
2025-07-30 17:53:32 | ERROR | core.fault_analyzer:__init__:56 | LLM未传入或不可用，故障分析功能将受限！
2025-07-30 17:53:44.449 | ERROR    | core.fault_analyzer:_initialize_chains:154 - ❌ 推理链初始化失败：LLM不可用
2025-07-30 17:53:44 | ERROR | core.fault_analyzer:_initialize_chains:154 | ❌ 推理链初始化失败：LLM不可用
2025-07-30 17:53:48.327 | ERROR    | data_processing.modern_chroma_manager:_init_collection:126 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 17:53:48 | ERROR | data_processing.modern_chroma_manager:_init_collection:126 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 17:54:59.244 | ERROR    | core.llm_manager:create_llm:53 - 无法导入DeepSeek，请确保安装了正确的依赖
2025-07-30 17:54:59 | ERROR | core.llm_manager:create_llm:53 | 无法导入DeepSeek，请确保安装了正确的依赖
2025-07-30 17:54:59.245 | ERROR    | core.fault_analyzer:__init__:57 - LLM初始化失败: LLM创建失败，请检查日志获取详细错误信息
2025-07-30 17:54:59 | ERROR | core.fault_analyzer:__init__:57 | LLM初始化失败: LLM创建失败，请检查日志获取详细错误信息
2025-07-30 17:54:59.245 | ERROR    | core.fault_analyzer:__init__:62 - LLM未传入或不可用，故障分析功能将受限！
2025-07-30 17:54:59 | ERROR | core.fault_analyzer:__init__:62 | LLM未传入或不可用，故障分析功能将受限！
2025-07-30 17:55:01.667 | ERROR    | core.fault_analyzer:_initialize_chains:160 - ❌ 推理链初始化失败：LLM不可用
2025-07-30 17:55:01 | ERROR | core.fault_analyzer:_initialize_chains:160 | ❌ 推理链初始化失败：LLM不可用
2025-07-30 17:55:07.662 | ERROR    | data_processing.modern_chroma_manager:_init_collection:126 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 17:55:07 | ERROR | data_processing.modern_chroma_manager:_init_collection:126 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 17:57:02.812 | ERROR    | core.llm_manager:create_llm:53 - 无法导入DeepSeek，请确保安装了正确的依赖
2025-07-30 17:57:02 | ERROR | core.llm_manager:create_llm:53 | 无法导入DeepSeek，请确保安装了正确的依赖
2025-07-30 17:57:02.813 | ERROR    | core.fault_analyzer:__init__:57 - LLM初始化失败: LLM创建失败，请检查日志获取详细错误信息
2025-07-30 17:57:02 | ERROR | core.fault_analyzer:__init__:57 | LLM初始化失败: LLM创建失败，请检查日志获取详细错误信息
2025-07-30 17:57:02.814 | ERROR    | core.fault_analyzer:__init__:62 - LLM未传入或不可用，故障分析功能将受限！
2025-07-30 17:57:02 | ERROR | core.fault_analyzer:__init__:62 | LLM未传入或不可用，故障分析功能将受限！
2025-07-30 17:57:13.797 | ERROR    | core.fault_analyzer:_initialize_chains:160 - ❌ 推理链初始化失败：LLM不可用
2025-07-30 17:57:13 | ERROR | core.fault_analyzer:_initialize_chains:160 | ❌ 推理链初始化失败：LLM不可用
2025-07-30 17:57:16.883 | ERROR    | data_processing.modern_chroma_manager:_init_collection:126 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-30 17:57:16 | ERROR | data_processing.modern_chroma_manager:_init_collection:126 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 13:20:08.777 | ERROR    | core.fault_analyzer:__init__:70 - LLM健康检查失败: 'DeepSeekClient' object has no attribute 'invoke'
2025-07-31 13:20:08 | ERROR | core.fault_analyzer:__init__:70 | LLM健康检查失败: 'DeepSeekClient' object has no attribute 'invoke'
2025-07-31 13:20:10.975 | ERROR    | core.fault_analyzer:_initialize_chains:174 - ❌ 故障分析链初始化失败: 'DeepSeekClient' object has no attribute 'get'
2025-07-31 13:20:10 | ERROR | core.fault_analyzer:_initialize_chains:174 | ❌ 故障分析链初始化失败: 'DeepSeekClient' object has no attribute 'get'
2025-07-31 13:20:10.975 | ERROR    | core.fault_analyzer:_initialize_chains:188 - ❌ 文档问答链初始化失败: 'DeepSeekClient' object has no attribute 'get'
2025-07-31 13:20:10 | ERROR | core.fault_analyzer:_initialize_chains:188 | ❌ 文档问答链初始化失败: 'DeepSeekClient' object has no attribute 'get'
2025-07-31 13:20:11.535 | ERROR    | data_processing.modern_chroma_manager:_init_collection:126 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 13:20:11 | ERROR | data_processing.modern_chroma_manager:_init_collection:126 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 13:41:15.162 | ERROR    | core.fault_analyzer:__init__:70 - LLM健康检查失败: 'DeepSeekClient' object has no attribute 'invoke'
2025-07-31 13:41:15 | ERROR | core.fault_analyzer:__init__:70 | LLM健康检查失败: 'DeepSeekClient' object has no attribute 'invoke'
2025-07-31 13:41:18.549 | ERROR    | core.fault_analyzer:_initialize_chains:174 - ❌ 故障分析链初始化失败: 'DeepSeekClient' object has no attribute 'get'
2025-07-31 13:41:18 | ERROR | core.fault_analyzer:_initialize_chains:174 | ❌ 故障分析链初始化失败: 'DeepSeekClient' object has no attribute 'get'
2025-07-31 13:41:18.550 | ERROR    | core.fault_analyzer:_initialize_chains:188 - ❌ 文档问答链初始化失败: 'DeepSeekClient' object has no attribute 'get'
2025-07-31 13:41:18 | ERROR | core.fault_analyzer:_initialize_chains:188 | ❌ 文档问答链初始化失败: 'DeepSeekClient' object has no attribute 'get'
2025-07-31 13:41:19.029 | ERROR    | data_processing.modern_chroma_manager:_init_collection:126 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 13:41:19 | ERROR | data_processing.modern_chroma_manager:_init_collection:126 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 13:53:18.241 | ERROR    | core.fault_analyzer:_initialize_chains:174 - ❌ 故障分析链初始化失败: 1 validation error for FaultAnalysisChain
llm
  Input should be a valid dictionary or instance of BaseLLM [type=model_type, input_value=<ui.deepseek_client.DeepS...t at 0x0000028775F811D0>, input_type=DeepSeekClient]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
2025-07-31 13:53:18 | ERROR | core.fault_analyzer:_initialize_chains:174 | ❌ 故障分析链初始化失败: 1 validation error for FaultAnalysisChain
llm
  Input should be a valid dictionary or instance of BaseLLM [type=model_type, input_value=<ui.deepseek_client.DeepS...t at 0x0000028775F811D0>, input_type=DeepSeekClient]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
2025-07-31 13:53:18.243 | ERROR    | core.fault_analyzer:_initialize_chains:188 - ❌ 文档问答链初始化失败: 1 validation error for DocumentQAChain
llm
  Input should be a valid dictionary or instance of BaseLLM [type=model_type, input_value=<ui.deepseek_client.DeepS...t at 0x0000028775F811D0>, input_type=DeepSeekClient]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
2025-07-31 13:53:18 | ERROR | core.fault_analyzer:_initialize_chains:188 | ❌ 文档问答链初始化失败: 1 validation error for DocumentQAChain
llm
  Input should be a valid dictionary or instance of BaseLLM [type=model_type, input_value=<ui.deepseek_client.DeepS...t at 0x0000028775F811D0>, input_type=DeepSeekClient]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
2025-07-31 13:58:02.958 | ERROR    | core.fault_analyzer:__init__:65 - LLM初始化失败: Can't instantiate abstract class DeepSeekLangChainLLM with abstract method _generate
2025-07-31 13:58:02 | ERROR | core.fault_analyzer:__init__:65 | LLM初始化失败: Can't instantiate abstract class DeepSeekLangChainLLM with abstract method _generate
2025-07-31 13:58:02.960 | ERROR    | core.fault_analyzer:__init__:70 - LLM未传入或不可用，故障分析功能将受限！
2025-07-31 13:58:02 | ERROR | core.fault_analyzer:__init__:70 | LLM未传入或不可用，故障分析功能将受限！
2025-07-31 13:58:06.781 | ERROR    | core.fault_analyzer:_initialize_chains:168 - ❌ 推理链初始化失败：LLM不可用
2025-07-31 13:58:06 | ERROR | core.fault_analyzer:_initialize_chains:168 | ❌ 推理链初始化失败：LLM不可用
2025-07-31 14:01:40.572 | ERROR    | core.fault_analyzer:__init__:65 - LLM初始化失败: Can't instantiate abstract class DeepSeekLangChainLLM with abstract method _generate
2025-07-31 14:01:40 | ERROR | core.fault_analyzer:__init__:65 | LLM初始化失败: Can't instantiate abstract class DeepSeekLangChainLLM with abstract method _generate
2025-07-31 14:01:40.577 | ERROR    | core.fault_analyzer:__init__:70 - LLM未传入或不可用，故障分析功能将受限！
2025-07-31 14:01:40 | ERROR | core.fault_analyzer:__init__:70 | LLM未传入或不可用，故障分析功能将受限！
2025-07-31 14:01:43.847 | ERROR    | core.fault_analyzer:_initialize_chains:168 - ❌ 推理链初始化失败：LLM不可用
2025-07-31 14:01:43 | ERROR | core.fault_analyzer:_initialize_chains:168 | ❌ 推理链初始化失败：LLM不可用
2025-07-31 14:01:46.633 | ERROR    | data_processing.modern_chroma_manager:_init_collection:126 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 14:01:46 | ERROR | data_processing.modern_chroma_manager:_init_collection:126 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 14:05:37.264 | ERROR    | core.fault_analyzer:__init__:65 - LLM初始化失败: Can't instantiate abstract class DeepSeekLangChainLLM with abstract method _generate
2025-07-31 14:05:37 | ERROR | core.fault_analyzer:__init__:65 | LLM初始化失败: Can't instantiate abstract class DeepSeekLangChainLLM with abstract method _generate
2025-07-31 14:05:37.268 | ERROR    | core.fault_analyzer:__init__:70 - LLM未传入或不可用，故障分析功能将受限！
2025-07-31 14:05:37 | ERROR | core.fault_analyzer:__init__:70 | LLM未传入或不可用，故障分析功能将受限！
2025-07-31 14:05:40.004 | ERROR    | core.fault_analyzer:_initialize_chains:168 - ❌ 推理链初始化失败：LLM不可用
2025-07-31 14:05:40 | ERROR | core.fault_analyzer:_initialize_chains:168 | ❌ 推理链初始化失败：LLM不可用
2025-07-31 14:05:42.571 | ERROR    | data_processing.modern_chroma_manager:_init_collection:126 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 14:05:42 | ERROR | data_processing.modern_chroma_manager:_init_collection:126 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 14:11:47.632 | ERROR    | core.fault_analyzer:__init__:65 - LLM初始化失败: Can't instantiate abstract class DeepSeekLangChainLLM with abstract method _generate
2025-07-31 14:11:47 | ERROR | core.fault_analyzer:__init__:65 | LLM初始化失败: Can't instantiate abstract class DeepSeekLangChainLLM with abstract method _generate
2025-07-31 14:11:47.634 | ERROR    | core.fault_analyzer:__init__:70 - LLM未传入或不可用，故障分析功能将受限！
2025-07-31 14:11:47 | ERROR | core.fault_analyzer:__init__:70 | LLM未传入或不可用，故障分析功能将受限！
2025-07-31 14:11:52.284 | ERROR    | core.fault_analyzer:_initialize_chains:168 - ❌ 推理链初始化失败：LLM不可用
2025-07-31 14:11:52 | ERROR | core.fault_analyzer:_initialize_chains:168 | ❌ 推理链初始化失败：LLM不可用
2025-07-31 14:14:19.091 | ERROR    | langchain_modules.chains.fault_analysis_chain:_initialize_tools:65 - 工具初始化失败: "OCRTool" object has no field "_ocr_processor"
2025-07-31 14:14:19 | ERROR | langchain_modules.chains.fault_analysis_chain:_initialize_tools:65 | 工具初始化失败: "OCRTool" object has no field "_ocr_processor"
2025-07-31 14:17:23.534 | ERROR    | data_processing.modern_chroma_manager:_init_collection:126 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 14:17:23 | ERROR | data_processing.modern_chroma_manager:_init_collection:126 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 14:23:19.022 | ERROR    | langchain_modules.chains.fault_analysis_chain:_initialize_tools:65 - 工具初始化失败: "OCRTool" object has no field "_ocr_processor"
2025-07-31 14:23:19 | ERROR | langchain_modules.chains.fault_analysis_chain:_initialize_tools:65 | 工具初始化失败: "OCRTool" object has no field "_ocr_processor"
2025-07-31 14:24:33.830 | ERROR    | data_processing.modern_chroma_manager:_init_collection:126 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 14:24:33 | ERROR | data_processing.modern_chroma_manager:_init_collection:126 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 14:27:39.738 | ERROR    | langchain_modules.chains.fault_analysis_chain:_initialize_tools:65 - 工具初始化失败: "OCRTool" object has no field "_ocr_processor"
2025-07-31 14:27:39 | ERROR | langchain_modules.chains.fault_analysis_chain:_initialize_tools:65 | 工具初始化失败: "OCRTool" object has no field "_ocr_processor"
2025-07-31 14:30:36.125 | ERROR    | data_processing.modern_chroma_manager:_init_collection:126 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 14:30:36 | ERROR | data_processing.modern_chroma_manager:_init_collection:126 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 14:33:57.748 | ERROR    | data_processing.modern_chroma_manager:_init_collection:126 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 14:33:57 | ERROR | data_processing.modern_chroma_manager:_init_collection:126 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 14:53:09.152 | ERROR    | data_processing.modern_chroma_manager:_init_collection:126 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 14:53:09 | ERROR | data_processing.modern_chroma_manager:_init_collection:126 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 14:57:11.431 | ERROR    | data_processing.modern_chroma_manager:_init_collection:126 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 14:57:11 | ERROR | data_processing.modern_chroma_manager:_init_collection:126 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 15:16:24.557 | ERROR    | data_processing.modern_chroma_manager:_init_collection:126 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 15:16:24 | ERROR | data_processing.modern_chroma_manager:_init_collection:126 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 15:20:03.289 | ERROR    | data_processing.modern_chroma_manager:_init_collection:126 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 15:20:03 | ERROR | data_processing.modern_chroma_manager:_init_collection:126 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 15:29:13.989 | ERROR    | data_processing.modern_chroma_manager:_init_collection:126 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 15:29:13 | ERROR | data_processing.modern_chroma_manager:_init_collection:126 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 15:45:16.399 | ERROR    | data_processing.modern_chroma_manager:_init_collection:126 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 15:45:16 | ERROR | data_processing.modern_chroma_manager:_init_collection:126 | ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 16:29:58.405 | ERROR    | langchain_modules.chains.fault_analysis_chain:_initialize_tools:65 - 工具初始化失败: "OCRTool" object has no field "_ocr_processor"
2025-07-31 16:48:14.470 | ERROR    | data_processing.modern_chroma_manager:_init_collection:126 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 17:08:07.613 | ERROR    | data_processing.modern_chroma_manager:_init_collection:126 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 17:19:13.710 | ERROR    | langchain_modules.chains.fault_analysis_chain:_initialize_tools:65 - 工具初始化失败: "OCRTool" object has no field "_ocr_processor"
2025-07-31 17:41:23.522 | ERROR    | data_processing.modern_chroma_manager:_init_collection:126 - ❌ 集合初始化失败: no such column: collections.topic
2025-07-31 18:10:51.796 | ERROR    | data_processing.modern_chroma_manager:_init_collection:126 - ❌ 集合初始化失败: no such column: collections.topic
2025-08-01 09:14:20.798 | ERROR    | data_processing.modern_chroma_manager:_init_collection:126 - ❌ 集合初始化失败: no such column: collections.topic
